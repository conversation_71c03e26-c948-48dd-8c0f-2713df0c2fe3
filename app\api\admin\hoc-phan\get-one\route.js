export const dynamic = 'force-dynamic';

import { connectToDB } from '@mongodb';
import Hoc<PERSON><PERSON> from '@models/HocPhan';

export const GET = async (req) => {
  try {
    await connectToDB();

    // Parse URL to get the `maMH` query parameter
    const url = new URL(req.url);
    const maMH = url.searchParams.get('maMH');

    if (!maMH) {
      return new Response(JSON.stringify({ message: "maMH is required" }), { status: 400 });
    }

    // Find the document by `maMH`
    const hocPhan = await HocPhan.findOne({ maMH });

    if (!hoc<PERSON>han) {
      return new Response(JSON.stringify({ message: "<PERSON><PERSON><PERSON><PERSON> not found" }), { status: 404 });
    }

    return new Response(JSON.stringify(hocPhan), { status: 200 });
  } catch (err) {
    console.error("Lỗi :", err);
    return new Response(JSON.stringify({ message: `Lỗi: ${err.message}` }), { status: 500 });
  }
};
