{"name": "chatapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.16.2", "@radix-ui/react-slot": "^1.1.0", "antd": "^5.19.3", "bcryptjs": "^2.4.3", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.11.1", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "lucide-react": "^0.414.0", "moment": "^2.30.1", "mongoose": "^8.5.1", "next": "14.2.5", "next-auth": "^4.24.7", "next-cloudinary": "^6.6.2", "nodemailer": "^6.9.14", "pusher": "^5.2.0", "pusher-js": "^8.4.0-rc2", "qs": "^6.12.3", "react": "^18", "react-dom": "^18", "react-highlight-words": "^0.20.0", "react-hook-form": "^7.52.1", "react-hot-toast": "^2.4.1", "xlsx": "^0.18.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"@types/parse-json": "^4.0.2", "@types/react-transition-group": "^4.4.11", "@types/webidl-conversions": "^7.0.3", "@types/whatwg-url": "^11.0.5", "postcss": "^8", "tailwindcss": "^3.4.1"}}