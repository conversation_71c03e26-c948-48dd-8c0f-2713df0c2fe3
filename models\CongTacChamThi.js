import mongoose from "mongoose";

const CongTacChamThiSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  hoc<PERSON>han: {
    type: String,
    required: true,
  },
  lopH<PERSON><PERSON>han: {
    type: String,
    required: true,
  },
  ky: {
    type: String,
    required: true,
  },
  canBoChamThi: {
    type: String,
    required: true,
  },
  soBaiCham: {
    type: Number,
    required: true,
  },
  soTietQuyChuan: {
    type: Number,
    required: true,
  },
  ghiChu: {
    type: String,
  },
  namHoc: {
    type: String,
  },
  type: {
    type: String,
    required: true,
  },
  hinhThuc: {
    type: String,  
  },
  thoiGian: {
    type: String,  
  },
},{
  timestamps: true,
});

const CongTacChamThi = mongoose.models.CongTacChamThi || mongoose.model("CongTacChamThi", CongTacChamThiSchema);

export default CongTacChamThi;
